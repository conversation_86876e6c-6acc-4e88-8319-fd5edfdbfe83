import { useEffect, useRef, useMemo, memo } from 'react';
import type { Position } from '@shared/schema';
// Importa gli stili per la visualizzazione mobile
import '../styles/mobile-table.css';
import { GaugeIcon, SatelliteIcon, CompassIcon, BatteryIcon, DownloadIcon, AlertTriangleIcon, ServerIcon, ClockIcon, MapPinIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { saveFile } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

// Nota: Le funzioni per i colori e i testi sono state spostate più in basso per evitare duplicazioni

interface PositionsTableProps {
  positions: Position[];
  selectedPosition: Position | null;
  onPositionSelect: (position: Position) => void;
  fromDate: Date | null;
  onFromDateChange: (date: Date | null) => void;
  toDate: Date | null;
  onToDateChange: (date: Date | null) => void;
  isMobile?: boolean;
}

// Array di colori da utilizzare ciclicamente per i diversi viaggi (deve corrispondere a quello usato nella mappa e nel selettore)
const trackColors = ['#3b82f6', '#ef4444', '#22c55e', '#f59e0b', '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16', '#d97706', '#6366f1'];

// Funzione per determinare a quale viaggio appartiene una posizione
export const getJourneyInfo = (position: Position, allPositions: Position[]) => {
  // Ordiniamo le posizioni cronologicamente (dalla più vecchia alla più recente)
  const chronologicalPositions = [...allPositions].sort((a, b) =>
    new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );

  // Troviamo tutte le posizioni di inizio (deviceStatus = 3)
  const journeyStartPositions = chronologicalPositions.filter(pos => pos.deviceStatus === 3);

  // Se non ci sono posizioni di inizio, restituisci null
  if (journeyStartPositions.length === 0) return null;

  // Trova il timestamp della posizione corrente
  const positionTime = new Date(position.timestamp).getTime();

  // Trova il viaggio a cui appartiene la posizione
  for (let i = 0; i < journeyStartPositions.length; i++) {
    const startPosition = journeyStartPositions[i];
    const startTime = new Date(startPosition.timestamp).getTime();

    // Trova la prossima posizione di inizio (se esiste)
    const nextStartPosition = journeyStartPositions[i + 1];
    const nextStartTime = nextStartPosition ? new Date(nextStartPosition.timestamp).getTime() : Infinity;

    // Se la posizione è compresa tra l'inizio di questo viaggio e l'inizio del prossimo
    if (positionTime >= startTime && positionTime < nextStartTime) {
      const journeyId = i + 1; // 1-based
      const color = trackColors[(journeyId - 1) % trackColors.length];
      return { journeyId, color };
    }
  }

  // Se non è stato trovato un viaggio, restituisci null
  return null;
};

// Funzione per convertire un colore hex in una versione più pastello
const toPastelColor = (hexColor: string): string => {
  // Rimuovi il carattere # se presente
  hexColor = hexColor.replace('#', '');

  // Converti il colore hex in RGB
  const r = parseInt(hexColor.substring(0, 2), 16);
  const g = parseInt(hexColor.substring(2, 4), 16);
  const b = parseInt(hexColor.substring(4, 6), 16);

  // Calcola la versione pastello (mescola con il bianco)
  const pastelFactor = 0.65; // Fattore di miscelazione con il bianco (0.0 - 1.0)
  const pr = Math.round(r + (255 - r) * pastelFactor);
  const pg = Math.round(g + (255 - g) * pastelFactor);
  const pb = Math.round(b + (255 - b) * pastelFactor);

  // Converti di nuovo in hex
  return `#${pr.toString(16).padStart(2, '0')}${pg.toString(16).padStart(2, '0')}${pb.toString(16).padStart(2, '0')}`;
};

// Funzione per calcolare il colore della velocità
const getSpeedColorFn = (speed: number | null) => {
  if (speed === null || speed === undefined) return "text-gray-500";
  if (speed < 30) return "text-green-600";
  if (speed < 70) return "text-blue-600";
  return "text-red-600";
};

// Funzione per calcolare il colore della batteria
const getBatteryColorFn = (level: number) => {
  if (level < 20) return "text-red-600";
  if (level < 50) return "text-amber-600";
  return "text-green-600";
};

// Funzione per ottenere il colore dello stato del dispositivo
const getDeviceStatusColorFn = (status: number) => {
  switch (status) {
    case 0: return "text-green-600"; // Online
    case 1: return "text-amber-600"; // Batteria
    case 2: return "text-red-600";   // Vibrazione
    case 3: return "text-blue-600";  // Inizio
    case 4: return "text-purple-600"; // Fine
    default: return "text-gray-600";
  }
};

// Funzione per ottenere il testo dello stato del dispositivo
const getDeviceStatusTextFn = (status: number) => {
  switch (status) {
    case 0: return "Online";
    case 1: return "Batteria";
    case 2: return "Vibrazione";
    case 3: return "Inizio";
    case 4: return "Fine";
    default: return "Sconosciuto";
  }
};

// Componente TableRow ottimizzato con memo per evitare rendering inutili
const TableRow = memo(({
  position,
  isSelected,
  isLatest,
  positionNumber,
  allPositions,
  onSelect,
  isMobile,
  setRowRef
}: {
  position: Position,
  isSelected: boolean,
  isLatest: boolean,
  positionNumber: number,
  allPositions: Position[],
  onSelect: (position: Position) => void,
  isMobile: boolean,
  setRowRef: (id: number, el: HTMLTableRowElement | null) => void
}) => {
  // Determina lo stile della riga in base alla selezione
  const rowClasses = isSelected
    ? "bg-teal-50 hover:bg-teal-100 cursor-pointer transition-colors duration-200"
    : "hover:bg-gray-50 cursor-pointer transition-colors duration-200";

  // Calcola il colore della velocità
  const speedColor = getSpeedColorFn(position.speed);

  // Determina il colore del cerchio in base al viaggio
  const journeyInfo = useMemo(() => getJourneyInfo(position, allPositions), [position.id, allPositions.length]);

  // Calcola lo stile del cerchio
  const circleStyle = useMemo(() => {
    if (journeyInfo) {
      // Usa il colore del viaggio in versione pastello
      return { backgroundColor: toPastelColor(journeyInfo.color) };
    } else if (isLatest) {
      // Se è l'ultima posizione ma non appartiene a un viaggio, usa il colore teal
      return { backgroundColor: '#99f6e4' }; // bg-teal-100
    } else {
      return { backgroundColor: '#f3f4f6' }; // bg-gray-100
    }
  }, [journeyInfo, isLatest]);

  // Formatta la data
  const formattedDate = useMemo(() => {
    const d = new Date(position.timestamp);
    return d.toLocaleString('it-IT', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }, [position.timestamp]);

  // Formatta la data del server
  const formattedServerDate = useMemo(() => {
    if (!position.serverTimestamp) return 'N/D';
    const d = new Date(position.serverTimestamp);
    return d.toLocaleString('it-IT', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }, [position.serverTimestamp]);

  return (
    <tr
      ref={el => setRowRef(position.id, el)}
      className={rowClasses}
      onClick={() => {
        console.log(`[${new Date().toISOString()}][PositionsTable.tsx][TableRow] Click su posizione ${position.id}, chiamata onSelect`);
        onSelect(position);
      }}
    >
      <td className={`px-3 py-2 whitespace-nowrap ${isMobile ? 'mobile-table-cell' : ''}`}>
        {isLatest && !journeyInfo ? (
          <span className={`inline-flex items-center justify-center h-5 w-5 rounded-full text-xs font-medium bg-teal-100 text-teal-800 ${isMobile ? 'mobile-table-text' : ''}`}>
            {positionNumber}
          </span>
        ) : (
          <span
            className={`inline-flex items-center justify-center h-5 w-5 rounded-full text-xs font-medium text-gray-800 ${isMobile ? 'mobile-table-text' : ''}`}
            style={circleStyle}
          >
            {positionNumber}
          </span>
        )}
      </td>
      <td className={`px-3 py-2 whitespace-nowrap ${isMobile ? 'mobile-table-cell' : ''}`}>
        <div className={`text-xs font-medium text-gray-800 ${isMobile ? 'mobile-table-text' : ''}`}>{formattedDate}</div>
      </td>
      {!isMobile && (
        <td className="px-3 py-2 whitespace-nowrap">
          <div className="text-xs font-medium text-gray-700">{formattedServerDate}</div>
        </td>
      )}
      {!isMobile && (
        <td className="px-3 py-2 whitespace-nowrap">
          <div className="text-xs text-gray-700">{position.latitude.toFixed(6)}, {position.longitude.toFixed(6)}</div>
        </td>
      )}
      <td className={`px-3 py-2 whitespace-nowrap ${isMobile ? 'mobile-table-cell' : ''}`}>
        <div className={`text-xs font-medium ${speedColor} ${isMobile ? 'mobile-table-text' : ''}`}>
          {position.speed !== null && position.speed !== undefined ? `${position.speed} km/h` : 'N/D'}
        </div>
      </td>
      {!isMobile && (
        <td className="px-3 py-2 whitespace-nowrap">
          <div className="text-xs font-medium text-gray-700">
            {position.course ? `${position.course}°` : 'N/D'}
          </div>
        </td>
      )}
      {!isMobile && (
        <td className="px-3 py-2 whitespace-nowrap">
          <div className="text-xs font-medium text-gray-700">
            {position.satellites ? 'Fixed' : 'Not Fixed'}
          </div>
        </td>
      )}
      <td className={`px-3 py-2 whitespace-nowrap ${isMobile ? 'mobile-table-cell' : ''}`}>
        <div className={`text-xs font-medium ${position.batteryLevel !== null && position.batteryLevel !== undefined ? getBatteryColorFn(position.batteryLevel) : 'text-gray-700'} ${isMobile ? 'mobile-table-text' : ''}`}>
          {position.batteryLevel !== null && position.batteryLevel !== undefined ? `${position.batteryLevel}%` : 'N/D'}
        </div>
      </td>
      <td className={`px-3 py-2 whitespace-nowrap ${isMobile ? 'mobile-table-cell' : ''}`}>
        <div className={`text-xs font-medium ${getDeviceStatusColorFn(position.deviceStatus)} ${isMobile ? 'mobile-table-text' : ''}`}>
          {getDeviceStatusTextFn(position.deviceStatus)}
        </div>
      </td>
    </tr>
  );
}, (prevProps, nextProps) => {
  // Funzione di confronto personalizzata per memo
  // Restituisce true se le props non sono cambiate (evitando il re-render)
  return (
    prevProps.position.id === nextProps.position.id &&
    prevProps.isSelected === nextProps.isSelected &&
    prevProps.isLatest === nextProps.isLatest &&
    prevProps.positionNumber === nextProps.positionNumber &&
    prevProps.isMobile === nextProps.isMobile &&
    // Non confrontiamo allPositions perché potrebbe causare troppi re-render
    // Confrontiamo solo la lunghezza dell'array
    prevProps.allPositions.length === nextProps.allPositions.length
  );
});

export default function PositionsTable({ positions, selectedPosition, onPositionSelect, fromDate, onFromDateChange, toDate, onToDateChange, isMobile = false }: PositionsTableProps) {
  console.log(`[${new Date().toISOString()}][PositionsTable.tsx] Render con onPositionSelect:`, typeof onPositionSelect);
  // Riferimento al contenitore della tabella per lo scrolling
  const tableContainerRef = useRef<HTMLDivElement>(null);
  // Mappa dei riferimenti alle righe della tabella
  const rowRefs = useRef<{ [id: string]: HTMLTableRowElement | null }>({});
  // Hook per i toast
  const { toast } = useToast();
  // Opzioni di formattazione della data (memorizzate per evitare di ricrearle ad ogni chiamata)
  const dateFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  } as Intl.DateTimeFormatOptions;

  // Cache per le date formattate
  const dateFormatCache = useRef<Map<string, string>>(new Map());

  // Format date con cache per migliorare le prestazioni
  const formatDate = (date: Date): string => {
    const timestamp = date.getTime().toString();

    // Verifica se la data è già nella cache
    if (dateFormatCache.current.has(timestamp)) {
      return dateFormatCache.current.get(timestamp)!;
    }

    // Formatta la data e memorizzala nella cache
    const d = new Date(date);
    const formatted = d.toLocaleString('it-IT', dateFormatOptions);

    // Limita la dimensione della cache a 1000 elementi
    if (dateFormatCache.current.size > 1000) {
      // Rimuovi la prima chiave (la più vecchia)
      const firstKey = dateFormatCache.current.keys().next().value;
      dateFormatCache.current.delete(firstKey);
    }

    // Aggiungi la nuova data alla cache
    dateFormatCache.current.set(timestamp, formatted);

    return formatted;
  };

  // Funzione per formattare la data per l'input datetime-local
  // Converte la data in formato locale (non UTC)
  const formatDateTimeForInput = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  // Utilizziamo le funzioni definite sopra per la coerenza

  // Nota: La funzionalità di limitazione del numero di messaggi è stata rimossa

  // Riferimenti per i timeout di debounce
  const fromDateTimeoutRef = useRef<number | null>(null);
  const toDateTimeoutRef = useRef<number | null>(null);

  // Gestisce il cambio della data di inizio con debounce
  const handleFromDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const now = new Date().toISOString();
    console.log(`[${now}][PositionsTable.tsx][handleFromDateChange] Cambio data inizio: ${e.target.value}`);

    // Cancella il timeout precedente se esiste
    if (fromDateTimeoutRef.current !== null) {
      clearTimeout(fromDateTimeoutRef.current);
    }

    // Crea un nuovo timeout per il debounce (300ms)
    fromDateTimeoutRef.current = window.setTimeout(() => {
      if (e.target.value) {
        // Crea una nuova data dall'input, che è già in ora locale
        const newDate = new Date(e.target.value);
        if (!isNaN(newDate.getTime())) {
          console.log(`[${now}][PositionsTable.tsx][handleFromDateChange] Nuova data valida: ${newDate.toISOString()}`);
          // Quando l'utente cambia la data, aggiorna la data
          onFromDateChange(newDate);
        } else {
          console.log(`[${now}][PositionsTable.tsx][handleFromDateChange] Data non valida: ${e.target.value}`);
        }
      } else {
        console.log(`[${now}][PositionsTable.tsx][handleFromDateChange] Campo vuoto, impostazione a null`);
        // Se il campo è vuoto, imposta la data a null
        onFromDateChange(null);
      }

      // Resetta il riferimento al timeout
      fromDateTimeoutRef.current = null;
    }, 300);
  };

  // Gestisce il cambio della data di fine con debounce
  const handleToDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const now = new Date().toISOString();
    console.log(`[${now}][PositionsTable.tsx][handleToDateChange] Cambio data fine: ${e.target.value}`);

    // Cancella il timeout precedente se esiste
    if (toDateTimeoutRef.current !== null) {
      clearTimeout(toDateTimeoutRef.current);
    }

    // Crea un nuovo timeout per il debounce (300ms)
    toDateTimeoutRef.current = window.setTimeout(() => {
      if (e.target.value) {
        // Crea una nuova data dall'input, che è già in ora locale
        const newDate = new Date(e.target.value);
        if (!isNaN(newDate.getTime())) {
          console.log(`[${now}][PositionsTable.tsx][handleToDateChange] Nuova data valida: ${newDate.toISOString()}`);
          // Quando l'utente cambia la data, aggiorna la data
          onToDateChange(newDate);
        } else {
          console.log(`[${now}][PositionsTable.tsx][handleToDateChange] Data non valida: ${e.target.value}`);
        }
      } else {
        console.log(`[${now}][PositionsTable.tsx][handleToDateChange] Campo vuoto, impostazione a null`);
        // Se il campo è vuoto, imposta la data a null
        onToDateChange(null);
      }

      // Resetta il riferimento al timeout
      toDateTimeoutRef.current = null;
    }, 300);
  };

  // Gestisce l'esportazione dei dati in formato KML
  const handleExportKML = async () => {

    try {
      if (!positions || positions.length === 0) {
        toast({
          title: "Errore",
          description: "Nessuna posizione disponibile da esportare",
          variant: "destructive"
        });
        return;
      }

      // Mostra un toast di caricamento
      toast({
        title: "Esportazione in corso",
        description: "Attendere il completamento del download..."
      });

      // Costruisci l'URL con i parametri
      let url = `/api/positions/export?format=kml&satelliteId=${positions[0]?.satelliteId || ''}`;

      // Aggiungi il parametro fromDate se presente
      if (fromDate) {
        url += `&fromDate=${fromDate.toISOString()}`;
      }

      // Aggiungi il parametro toDate se presente
      if (toDate) {
        url += `&toDate=${toDate.toISOString()}`;
      }

      // Effettua la richiesta al server
      const response = await fetch(url, {
        method: 'GET',
        credentials: 'include'
      });

      // Verifica se la risposta è valida
      if (!response.ok) {
        throw new Error(`Errore durante l'esportazione: ${response.status} ${response.statusText}`);
      }

      // Ottieni il nome del file dall'header Content-Disposition
      let filename = `positions_${positions[0]?.satelliteId || 'all'}.kml`;
      const contentDisposition = response.headers.get('Content-Disposition');
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1];
        }
      }

      // Ottieni i dati dalla risposta
      const data = await response.text();

      // Conta le posizioni (approssimazione contando i Placemark)
      const matches = data.match(/<Placemark>/g);
      const positionsCount = matches ? matches.length : 0;

      // Salva il file
      saveFile(data, filename, 'application/vnd.google-earth.kml+xml');

      // Mostra un toast di successo con il conteggio delle posizioni
      toast({
        title: "Esportazione completata",
        description: `File ${filename} salvato con successo (${positionsCount-1} posizioni)`
      });
    } catch (error) {
      console.error("Errore durante l'esportazione KML:", error);
      toast({
        title: "Errore",
        description: "Impossibile esportare i dati in formato KML",
        variant: "destructive"
      });
    }
  };

  // Riferimento alla posizione selezionata precedente
  const prevSelectedIdRef = useRef<number | null>(null);

  // Effetto per scorrere alla posizione selezionata quando cambia
  useEffect(() => {
    const now = new Date().toISOString();

    // Verifica se la posizione selezionata è cambiata
    if (!selectedPosition || selectedPosition.id === prevSelectedIdRef.current) {
      return;
    }

    // Aggiorna il riferimento alla posizione selezionata
    prevSelectedIdRef.current = selectedPosition.id;

    console.log(`[${now}][PositionsTable.tsx][useEffect-scrollToSelected] Verifica scroll alla posizione selezionata`);
    console.log(`[${now}][PositionsTable.tsx][useEffect-scrollToSelected] Posizione selezionata: id=${selectedPosition.id}, timestamp=${new Date(selectedPosition.timestamp).toISOString()}`);

    // Utilizziamo un timeout per dare tempo al DOM di aggiornarsi
    setTimeout(() => {
      if (selectedPosition && tableContainerRef.current) {
        const selectedRow = rowRefs.current[selectedPosition.id];
        if (selectedRow) {
          console.log(`[${now}][PositionsTable.tsx][useEffect-scrollToSelected] Riga trovata nella tabella, calcolo posizione scroll`);

          // Calcola la posizione di scroll per centrare la riga selezionata
          const container = tableContainerRef.current;
          const rowTop = selectedRow.offsetTop;
          const rowHeight = selectedRow.offsetHeight;
          const containerHeight = container.clientHeight;

          // Centra la riga nella vista
          const scrollTo = rowTop - (containerHeight / 2) + (rowHeight / 2);
          console.log(`[${now}][PositionsTable.tsx][useEffect-scrollToSelected] Parametri scroll: rowTop=${rowTop}, rowHeight=${rowHeight}, containerHeight=${containerHeight}, scrollTo=${scrollTo}`);

          // Esegui lo scroll con animazione fluida ma più veloce
          container.scrollTo({
            top: scrollTo,
            behavior: 'smooth'
          });
          console.log(`[${now}][PositionsTable.tsx][useEffect-scrollToSelected] Scroll eseguito`);
        } else {
          console.log(`[${now}][PositionsTable.tsx][useEffect-scrollToSelected] Riga non trovata nei riferimenti`);
        }
      } else if (!tableContainerRef.current) {
        console.log(`[${now}][PositionsTable.tsx][useEffect-scrollToSelected] Riferimento al container tabella non disponibile`);
      }
    }, 50); // Breve timeout per dare tempo al DOM di aggiornarsi
  }, [selectedPosition]);

  return (
    <div className="overflow-x-auto h-full">

      {/* Tabella con scrolling */}
      <div ref={tableContainerRef} className="h-full overflow-y-auto scrollbar-visible">
        <table className="min-w-full divide-y divide-gray-200 rounded-lg overflow-hidden mb-2">
          <thead className="bg-gradient-to-r from-teal-100 to-white sticky top-0 z-10">
            <tr>
            <th scope="col" className={`px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider ${isMobile ? 'mobile-table-header mobile-table-cell' : ''}`}>
              #
            </th>
            <th scope="col" className={`px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider ${isMobile ? 'mobile-table-header mobile-table-cell' : ''}`}>
              <div className="flex items-center">
                <ClockIcon className={`h-3 w-3 mr-1 ${isMobile ? 'mobile-table-icon' : ''}`} />
                <span>Data dispositivo</span>
              </div>
            </th>
            {!isMobile && (
              <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                <div className="flex items-center">
                  <ServerIcon className="h-3 w-3 mr-1" />
                  <span>Data server</span>
                </div>
              </th>
            )}
            {!isMobile && (
              <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                <div className="flex items-center">
                  <MapPinIcon className="h-3 w-3 mr-1" />
                  <span>Coordinate</span>
                </div>
              </th>
            )}
            <th scope="col" className={`px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider ${isMobile ? 'mobile-table-header mobile-table-cell' : ''}`}>
              <div className="flex items-center">
                <GaugeIcon className={`h-3 w-3 mr-1 ${isMobile ? 'mobile-table-icon' : ''}`} />
                <span>Velocità</span>
              </div>
            </th>
            {!isMobile && (
              <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                <div className="flex items-center">
                  <CompassIcon className="h-3 w-3 mr-1" />
                  <span>Direzione</span>
                </div>
              </th>
            )}
            {!isMobile && (
              <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                <div className="flex items-center">
                  <SatelliteIcon className="h-3 w-3 mr-1" />
                  <span>GPS</span>
                </div>
              </th>
            )}
            <th scope="col" className={`px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider ${isMobile ? 'mobile-table-header mobile-table-cell' : ''}`}>
              <div className="flex items-center">
                <BatteryIcon className={`h-3 w-3 mr-1 ${isMobile ? 'mobile-table-icon' : ''}`} />
                <span>Batteria</span>
              </div>
            </th>
            <th scope="col" className={`px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider ${isMobile ? 'mobile-table-header mobile-table-cell' : ''}`}>
              <div className="flex items-center">
                <AlertTriangleIcon className={`h-3 w-3 mr-1 ${isMobile ? 'mobile-table-icon' : ''}`} />
                <span>Stato</span>
              </div>
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-100 max-h-[70vh] overflow-y-auto">
          {/* Utilizziamo useMemo per evitare di ricreare l'array di righe ad ogni render */}
          {useMemo(() => {
            // Funzione per impostare i riferimenti alle righe
            const setRowRef = (id: number, el: HTMLTableRowElement | null) => {
              rowRefs.current[id] = el;
            };

            // Calcola le righe della tabella solo quando necessario
            return positions.map((position, index) => {
              // Determina se questa riga è selezionata
              const isSelected = selectedPosition?.id === position.id;

              // Determina se questa è la posizione più recente
              const isLatest = index === 0 && position.id === positions[0].id;

              // Calcola il numero di posizione
              const originalIndex = positions.findIndex(pos => pos.id === position.id);
              const positionNumber = positions.length - originalIndex;

              // Renderizza la riga utilizzando il componente ottimizzato
              return (
                <TableRow
                  key={position.id}
                  position={position}
                  isSelected={isSelected}
                  isLatest={isLatest}
                  positionNumber={positionNumber}
                  allPositions={positions}
                  onSelect={(pos) => {
                    console.log(`[${new Date().toISOString()}][PositionsTable.tsx] onSelect wrapper chiamato per posizione ${pos.id}`);
                    onPositionSelect(pos);
                  }}
                  isMobile={isMobile}
                  setRowRef={setRowRef}
                />
              );
            });
          }, [positions, selectedPosition, isMobile, onPositionSelect])}
        </tbody>
        </table>
      </div>
    </div>
  );
}

