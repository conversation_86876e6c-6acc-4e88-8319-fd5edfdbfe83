import { satelliteInterface } from './satellite-interface';
import { InsertPosition } from '@shared/schema';

// Esportiamo una funzione globale per aggiungere posizioni GPS dall'esterno
export let addGPSPosition: (deviceId: string, latitude: number, longitude: number, speed: number, satellites: number, course: number, batteryLevel?: number, date?: Date, deviceStatus?: number) => void;

// Esportiamo una funzione globale per aggiornare lo stato di connessione del satellitare
export let updateConnectionStatus: (connected: boolean, deviceId?: string) => void;

// Dizionario che contiene le ultime posizioni per ogni satellitare
// La chiave è l'ID del satellite, il valore è un array di posizioni
interface SatellitePositionsMap {
  [key: string]: InsertPosition[];
}

/**
 * Implementazione del sistema di gestione del satellitare.
 * Supporta più dispositivi con ID satellitare diverso.
 */
export function initializeSatellite(satelliteInterface: any) {
  // La esportiamo direttamente come variabile del modulo
  const addGPSPositionInternal = async (deviceId: string, latitude: number, longitude: number, speed: number, satellites: number, course: number, batteryLevel?: number, date?: Date, deviceStatus: number = 0) => {
    const now = new Date().toISOString();
    console.log(`[${now}][my-satellite-code.ts][addGPSPositionInternal] Aggiunta posizione per deviceId=${deviceId}, lat=${latitude}, long=${longitude}, status=${deviceStatus}`);

    // Crea una nuova posizione dal dispositivo GPS
    const newPosition: InsertPosition = {
      latitude,
      longitude,
      speed,
      satellites,
      course,
      batteryLevel,
      satelliteId: deviceId
    };

    try {
      console.log(`[${now}][my-satellite-code.ts][addGPSPositionInternal] Ricevuta posizione da dispositivo GPS (${deviceId}): ${latitude}, ${longitude}, Stato: ${deviceStatus}`);

      // Verifica se l'ultima posizione salvata per questo dispositivo aveva stato = 4 (Fine)
      // In tal caso, dobbiamo aggiungere una posizione con stato = 3 (Inizio) prima di aggiungere la posizione attuale
      if (deviceStatus !== 4) { // Non eseguiamo questa logica se stiamo già aggiungendo un messaggio di Fine
        const lastPositions = await satelliteInterface.getPositions(1, deviceId);

        if (lastPositions.length > 0) {
          // C'è almeno una posizione precedente
          const lastPosition = lastPositions[0]; // L'array è ordinato con la posizione più recente per prima

          if (lastPosition.deviceStatus === 4) { // L'ultima posizione era di tipo "Fine"
            deviceStatus = 3; // Imposta lo stato a "Inizio"
            console.log(`Aggiunta posizione di inizio alle coordinate: ${lastPosition.latitude}, ${lastPosition.longitude}`);
          }
        } else {
          // Non ci sono posizioni precedenti, è la prima posizione per questo dispositivo
          deviceStatus = 3; // Imposta lo stato a "Inizio"
          console.log(`Aggiunta posizione di inizio alle coordinate: ${latitude}, ${longitude}`);
        }
      } else {
        // Se stiamo aggiungendo un messaggio di Fine (deviceStatus = 4), verifichiamo se l'ultimo messaggio era già di Fine
        const lastPositions = await satelliteInterface.getPositions(1, deviceId);

        if (lastPositions.length > 0) {
          const lastPosition = lastPositions[0]; // L'array è ordinato con la posizione più recente per prima

          if (lastPosition.deviceStatus === 4) { // L'ultima posizione era già di tipo "Fine"
            console.log(`[${now}][my-satellite-code.ts][addGPSPositionInternal] Rilevati due messaggi di Fine consecutivi per deviceId=${deviceId}`);

            // Crea un messaggio di Inizio con timestamp precedente al messaggio di Fine corrente
            const currentTimestamp = date || new Date();
            const startTimestamp = new Date(currentTimestamp.getTime() - 5000); // 5 secondi prima

            // Crea la posizione di Inizio usando le coordinate dell'ultimo messaggio di Fine
            const startPosition: InsertPosition = {
              latitude: lastPosition.latitude,
              longitude: lastPosition.longitude,
              speed: 0,
              satellites: lastPosition.satellites || 0,
              course: lastPosition.course || 0,
              batteryLevel: lastPosition.batteryLevel || 0,
              satelliteId: deviceId
            };

            console.log(`[${now}][my-satellite-code.ts][addGPSPositionInternal] Aggiunta posizione di Inizio automatica alle coordinate: ${lastPosition.latitude}, ${lastPosition.longitude}, timestamp: ${startTimestamp.toISOString()}`);

            // Aggiungi il messaggio di Inizio
            await satelliteInterface.addPosition(startPosition, deviceId, startTimestamp, 3);
          }
        }
      }

      // Aggiungi la posizione
      console.log(`[${now}][my-satellite-code.ts][addGPSPositionInternal] Chiamata a satelliteInterface.addPosition per deviceId=${deviceId}, status=${deviceStatus}`);
      const position = await satelliteInterface.addPosition(newPosition, deviceId, date, deviceStatus);
      console.log(`[${now}][my-satellite-code.ts][addGPSPositionInternal] Posizione aggiunta con successo, id=${position.id}`);

    } catch (error) {
      console.error(`Errore nell'aggiunta della posizione GPS: ${error}`);
    }
  };

  // Assegniamo la funzione alla variabile esportata
  addGPSPosition = addGPSPositionInternal;

  // La esponiamo anche come variabile globale
  (global as any).addGPSPosition = addGPSPositionInternal;

  // Definiamo la funzione per aggiornare lo stato di connessione
  const updateConnectionStatusInternal = (connected: boolean, deviceId?: string) => {
    try {
      const now = new Date().toISOString();
      console.log(`[${now}][my-satellite-code.ts][updateConnectionStatusInternal] Stato connessione satellitare: connected=${connected}, deviceId=${deviceId || 'sconosciuto'}`);

      // Invia lo stato di connessione attraverso l'interfaccia satellite
      console.log(`[${now}][my-satellite-code.ts][updateConnectionStatusInternal] Chiamata a satelliteInterface.updateConnectionStatus`);
      satelliteInterface.updateConnectionStatus(connected, deviceId);
      console.log(`[${now}][my-satellite-code.ts][updateConnectionStatusInternal] Chiamata a satelliteInterface.updateConnectionStatus completata con successo`);
    } catch (error) {
      console.error(`[${new Date().toISOString()}][my-satellite-code.ts][updateConnectionStatusInternal] Errore nell'aggiornamento dello stato di connessione:`, error);
    }
  };

  // Assegniamo la funzione alla variabile esportata
  updateConnectionStatus = updateConnectionStatusInternal;
  // La esponiamo anche come variabile globale
  (global as any).updateConnectionStatus = updateConnectionStatusInternal;

  // Lo assegniamo anche all'oggetto window in caso di utilizzo lato client
  if (typeof window !== 'undefined') {
    (window as any).addGPSPosition = addGPSPositionInternal;
    (window as any).updateConnectionStatus = updateConnectionStatusInternal;
  }
}

// Funzione di utilità che potrebbe essere usata per aggiungere manualmente un nuovo satellite
export function addSatellite(satelliteId: string, initialPosition?: InsertPosition) {
  console.log(`Aggiunto nuovo satellite: ${satelliteId}`);

  // Se viene fornita una posizione iniziale, la usiamo
  if (initialPosition) {
    try {
      // Assicuriamoci che l'ID del satellite sia impostato correttamente
      const position: InsertPosition = {
        ...initialPosition,
        satelliteId
      };

      // Aggiungi la posizione iniziale attraverso l'interfaccia
      satelliteInterface.addPosition(position, satelliteId);
    } catch (error) {
      console.error(`Errore nell'aggiunta della posizione iniziale per il satellite ${satelliteId}: ${error}`);
    }
  }

  return satelliteId;
}

